# DropShelf - Windows 生产力工具

一个可以通过鼠标抖动手势召唤的浮动文件架，支持临时存储和拖拽文件。

## ✨ 功能特点

- 🖱️ **手势召唤**：拖拽文件时快速左右抖动鼠标召唤shelf
- 📦 **统一图标**：所有文件显示为一个图标，显示文件数量
- 🔄 **展开/收起**：单击图标展开查看所有文件
- 🚫 **自动去重**：相同文件不会重复添加
- 🖱️ **右键关闭**：右键点击窗口任意位置关闭shelf
- ⌨️ **快捷键**：按Esc键关闭shelf

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python improved_main.py
```

## 📖 使用方法

1. **召唤shelf**：
   - 从文件管理器拖拽任意文件
   - 拖拽过程中快速左右抖动鼠标
   - shelf窗口出现在鼠标附近

2. **管理文件**：
   - 拖拽文件到shelf进行存储
   - 单击图标展开查看所有文件
   - 拖拽图标可一次性拖出所有文件
   - 拖拽展开列表中的单个文件

3. **关闭shelf**：
   - 按Esc键关闭
   - 右键点击窗口任意位置关闭

## 📁 项目结构

```
dropshelf/
├── improved_main.py          # 主应用程序
├── improved_shelf_widget.py  # shelf窗口组件
├── gesture_detector.py       # 手势检测器
├── requirements.txt          # 依赖项列表
└── README.md                # 项目说明
```

## 🔧 系统要求

- Windows 10/11
- Python 3.10+
- PyQt6
- pynput
- Pillow
- pywin32

## 🎯 技术特点

- **智能手势检测**：只在拖拽文件时检测抖动
- **状态感知**：窗口显示时停止手势检测，避免位置变化
- **现代化UI**：半透明背景，圆角设计
- **多线程架构**：手势检测在后台线程运行
- **事件驱动**：使用Qt信号进行组件间通信

---

**DropShelf** - 让文件管理更高效！ 🎉
