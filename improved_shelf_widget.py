"""
改进版本的DropShelf组件
实现统一图标显示、展开/收起、去重等功能
"""

import os
import time
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QScrollArea, QFrame, QApplication)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData, QUrl, QPoint
from PyQt6.QtGui import (QColor, QPainter, QBrush, QLinearGradient,
                         QDrag, QPixmap, QFont, QPen)


class DraggableFileItem(QLabel):
    """可拖拽的文件项组件"""

    def __init__(self, file_path, parent=None):
        filename = os.path.basename(file_path)
        super().__init__(f"📄 {filename}", parent)

        self.file_path = file_path
        self.drag_start_position = QPoint()
        self.mouse_press_time = 0

        self.setStyleSheet("""
            QLabel {
                background: rgba(70, 70, 70, 150);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                margin: 1px;
            }
            QLabel:hover {
                background: rgba(90, 90, 90, 200);
            }
        """)
        self.setToolTip(file_path)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.pos()
            self.mouse_press_time = time.time()
        elif event.button() == Qt.MouseButton.RightButton:
            # 右键点击：向上传递到父窗口
            print("🖱️ 文件项右键点击 - 关闭shelf")
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'hide_shelf'):
                    parent_widget.hide_shelf()
                    return
                parent_widget = parent_widget.parent()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if not hasattr(self, 'drag_start_position'):
            return

        # 检查移动距离
        move_distance = (event.pos() - self.drag_start_position).manhattanLength()
        if move_distance < QApplication.startDragDistance():
            return

        # 检查按下时间（避免误触发）
        if hasattr(self, 'mouse_press_time'):
            hold_time = time.time() - self.mouse_press_time
            if hold_time < 0.1:  # 按下时间少于100ms，不开始拖拽
                return

        # 开始拖拽单个文件
        self.start_drag()

    def start_drag(self):
        """开始拖拽"""
        drag = QDrag(self)
        mime_data = QMimeData()

        # 添加文件URL
        urls = [QUrl.fromLocalFile(self.file_path)]
        mime_data.setUrls(urls)
        drag.setMimeData(mime_data)

        # 创建拖拽图标
        pixmap = self.create_drag_pixmap()
        drag.setPixmap(pixmap)
        drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))

        filename = os.path.basename(self.file_path)
        print(f"🚀 开始拖拽单个文件: {filename}")

        # 执行拖拽
        drop_action = drag.exec(Qt.DropAction.CopyAction | Qt.DropAction.MoveAction)

        if drop_action == Qt.DropAction.MoveAction:
            # 通知父组件移除该文件
            parent_widget = self.parent()
            while parent_widget and not hasattr(parent_widget, 'remove_file'):
                parent_widget = parent_widget.parent()
            if parent_widget and hasattr(parent_widget, 'remove_file'):
                parent_widget.remove_file(self.file_path)

    def create_drag_pixmap(self):
        """创建拖拽图标"""
        filename = os.path.basename(self.file_path)
        pixmap = QPixmap(120, 40)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景
        painter.setBrush(QBrush(QColor(60, 60, 60, 200)))
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawRoundedRect(pixmap.rect(), 6, 6)

        # 绘制文件图标和名称
        painter.setPen(QPen(QColor(255, 255, 255)))
        font = QFont()
        font.setPointSize(9)
        painter.setFont(font)

        text = f"📄 {filename[:10]}..." if len(filename) > 10 else f"📄 {filename}"
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, text)

        painter.end()
        return pixmap


class FileCollectionWidget(QWidget):
    """文件集合组件 - 统一显示所有文件"""
    
    # 信号
    files_dragged_out = pyqtSignal(list)  # 文件被拖出时发出
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_paths = set()  # 使用set避免重复
        self.is_expanded = False
        self.drag_start_position = QPoint()
        
        self.setup_ui()
        self.update_display()
        
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(120, 80)
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(4)
        
        # 图标标签
        self.icon_label = QLabel("📦")
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet("font-size: 24px;")
        
        # 文件数量标签
        self.count_label = QLabel("0 个文件")
        self.count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(9)
        self.count_label.setFont(font)
        
        self.main_layout.addWidget(self.icon_label)
        self.main_layout.addWidget(self.count_label)
        
        # 展开的文件列表（初始隐藏）
        self.expanded_widget = QFrame()
        self.expanded_widget.setFrameStyle(QFrame.Shape.Box)
        self.expanded_widget.setStyleSheet("""
            QFrame {
                background: rgba(50, 50, 50, 200);
                border: 1px solid rgba(100, 100, 100, 150);
                border-radius: 6px;
                margin-top: 5px;
            }
        """)
        self.expanded_widget.hide()
        
        # 滚动区域用于文件列表
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMaximumHeight(200)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
        """)
        
        # 文件列表容器
        self.file_list_widget = QWidget()
        self.file_list_layout = QVBoxLayout(self.file_list_widget)
        self.file_list_layout.setContentsMargins(5, 5, 5, 5)
        self.file_list_layout.setSpacing(2)
        
        self.scroll_area.setWidget(self.file_list_widget)
        
        expanded_layout = QVBoxLayout(self.expanded_widget)
        expanded_layout.setContentsMargins(5, 5, 5, 5)
        expanded_layout.addWidget(self.scroll_area)
        
        self.main_layout.addWidget(self.expanded_widget)
        
        # 设置样式
        self.update_style()
        
    def update_style(self):
        """更新样式"""
        self.setStyleSheet("""
            FileCollectionWidget {
                background: rgba(60, 60, 60, 180);
                border: 1px solid rgba(80, 80, 80, 200);
                border-radius: 8px;
            }
            FileCollectionWidget:hover {
                background: rgba(80, 80, 80, 200);
                border: 1px solid rgba(120, 120, 120, 255);
            }
        """)
        
        self.count_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 220);
                background: transparent;
                border: none;
            }
        """)
    
    def add_files(self, file_paths):
        """添加文件（自动去重）"""
        added_count = 0
        for file_path in file_paths:
            if file_path not in self.file_paths:
                self.file_paths.add(file_path)
                added_count += 1
                self.add_file_to_list(file_path)
        
        if added_count > 0:
            self.update_display()
            print(f"📦 添加了 {added_count} 个新文件，总计 {len(self.file_paths)} 个文件")
        else:
            print("⚠️ 所有文件都已存在，未添加重复文件")
    
    def add_file_to_list(self, file_path):
        """添加文件到展开列表"""
        # 使用自定义的可拖拽文件项组件
        file_item = DraggableFileItem(file_path, self)
        self.file_list_layout.addWidget(file_item)
    
    def update_display(self):
        """更新显示"""
        count = len(self.file_paths)
        if count == 0:
            self.count_label.setText("空")
            self.icon_label.setText("📦")
        elif count == 1:
            self.count_label.setText("1 个文件")
            self.icon_label.setText("📄")
        else:
            self.count_label.setText(f"{count} 个文件")
            self.icon_label.setText("📦")
        
        # 调整子组件大小以适应内容
        if self.is_expanded and count > 0:
            # 展开状态：让子组件适应父窗口大小
            # 不设置固定大小，让布局自动调整
            self.setMinimumSize(170, 150)
            self.setMaximumSize(190, 320)
        else:
            # 收起状态：固定大小
            self.setFixedSize(120, 80)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.pos()
            self.mouse_press_time = time.time()
        elif event.button() == Qt.MouseButton.RightButton:
            # 右键点击：向上传递到父窗口
            print("🖱️ 文件集合右键点击 - 关闭shelf")
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'hide_shelf'):
                    parent_widget.hide_shelf()
                    return
                parent_widget = parent_widget.parent()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 处理单击展开"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否是短时间点击（不是拖拽）
            if hasattr(self, 'mouse_press_time'):
                click_duration = time.time() - self.mouse_press_time
                if click_duration < 0.3:  # 300ms内的点击
                    # 检查鼠标移动距离
                    move_distance = (event.pos() - self.drag_start_position).manhattanLength()
                    if move_distance < 10:  # 移动距离小于10像素
                        # 只有多于1个文件时才能展开
                        if len(self.file_paths) > 1:
                            print("🔄 单击图标 - 切换展开状态")
                            self.toggle_expansion()
                        elif len(self.file_paths) == 1:
                            print("ℹ️ 只有1个文件，无需展开")
                        return
        super().mouseReleaseEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return
        
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
        
        # 开始拖拽所有文件
        if len(self.file_paths) > 0:
            self.start_all_files_drag()
    
    def start_all_files_drag(self):
        """开始拖拽所有文件"""
        if not self.file_paths:
            return
        
        # 创建拖拽对象
        drag = QDrag(self)
        mime_data = QMimeData()
        
        # 添加所有文件URL
        urls = [QUrl.fromLocalFile(path) for path in self.file_paths]
        mime_data.setUrls(urls)
        drag.setMimeData(mime_data)
        
        # 创建拖拽图标
        pixmap = self.create_drag_pixmap()
        drag.setPixmap(pixmap)
        drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
        
        print(f"🚀 开始拖拽 {len(self.file_paths)} 个文件")
        
        # 执行拖拽
        drop_action = drag.exec(Qt.DropAction.CopyAction | Qt.DropAction.MoveAction)
        
        if drop_action == Qt.DropAction.MoveAction:
            # 移动操作：清空文件列表
            self.clear_files()
            self.files_dragged_out.emit(list(self.file_paths))
    
    def start_single_file_drag(self, event, file_path):
        """开始拖拽单个文件"""
        if event.button() != Qt.MouseButton.LeftButton:
            return
        
        # 创建拖拽对象
        drag = QDrag(self)
        mime_data = QMimeData()
        
        # 添加单个文件URL
        urls = [QUrl.fromLocalFile(file_path)]
        mime_data.setUrls(urls)
        drag.setMimeData(mime_data)
        
        # 创建单文件拖拽图标
        pixmap = self.create_single_file_drag_pixmap(file_path)
        drag.setPixmap(pixmap)
        drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
        
        filename = os.path.basename(file_path)
        print(f"🚀 开始拖拽单个文件: {filename}")
        
        # 执行拖拽
        drop_action = drag.exec(Qt.DropAction.CopyAction | Qt.DropAction.MoveAction)
        
        if drop_action == Qt.DropAction.MoveAction:
            # 移动操作：从列表中移除该文件
            self.remove_file(file_path)
    
    def create_drag_pixmap(self):
        """创建拖拽图标"""
        pixmap = QPixmap(100, 60)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        painter.setBrush(QBrush(QColor(60, 60, 60, 200)))
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawRoundedRect(pixmap.rect(), 8, 8)
        
        # 绘制图标和文字
        painter.setPen(QPen(QColor(255, 255, 255)))
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        painter.setFont(font)
        
        text = f"📦 {len(self.file_paths)} 个文件"
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, text)
        
        painter.end()
        return pixmap
    
    def create_single_file_drag_pixmap(self, file_path):
        """创建单文件拖拽图标"""
        filename = os.path.basename(file_path)
        pixmap = QPixmap(120, 40)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        painter.setBrush(QBrush(QColor(60, 60, 60, 200)))
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawRoundedRect(pixmap.rect(), 6, 6)
        
        # 绘制文件图标和名称
        painter.setPen(QPen(QColor(255, 255, 255)))
        font = QFont()
        font.setPointSize(9)
        painter.setFont(font)
        
        text = f"📄 {filename[:10]}..." if len(filename) > 10 else f"📄 {filename}"
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, text)
        
        painter.end()
        return pixmap
    
    def toggle_expansion(self):
        """切换展开/收起状态"""
        self.is_expanded = not self.is_expanded
        
        if self.is_expanded:
            self.expanded_widget.show()
            print("📂 展开文件列表")
        else:
            self.expanded_widget.hide()
            print("📁 收起文件列表")
        
        self.update_display()
        
        # 通知父组件调整大小
        if self.parent():
            self.parent().adjust_size()
    
    def remove_file(self, file_path):
        """移除文件"""
        if file_path in self.file_paths:
            self.file_paths.remove(file_path)

            # 从UI中移除
            for i in range(self.file_list_layout.count()):
                item = self.file_list_layout.itemAt(i)
                if item and hasattr(item.widget(), 'file_path'):
                    if item.widget().file_path == file_path:
                        widget = item.widget()
                        self.file_list_layout.removeWidget(widget)
                        widget.deleteLater()
                        break

            # 检查是否需要自动收起
            if len(self.file_paths) == 0:
                # 没有文件了，自动收起并通知父组件显示占位符
                if self.is_expanded:
                    self.is_expanded = False
                    self.expanded_widget.hide()
                    print("📁 没有文件了，自动收起")
                # 通知父组件处理空文件状态
                if self.parent():
                    self.parent().on_files_dragged_out([])
                return
            elif len(self.file_paths) == 1 and self.is_expanded:
                # 只剩1个文件且处于展开状态，自动收起
                self.is_expanded = False
                self.expanded_widget.hide()
                print("📁 只剩1个文件，自动收起")

            self.update_display()
            filename = os.path.basename(file_path)
            print(f"🗑️ 移除文件: {filename}")

            # 通知父组件调整大小
            if self.parent():
                self.parent().adjust_size()
    
    def clear_files(self):
        """清空所有文件"""
        self.file_paths.clear()

        # 清空UI
        while self.file_list_layout.count():
            item = self.file_list_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.is_expanded = False
        self.expanded_widget.hide()
        self.update_display()
        print("🗑️ 清空所有文件")

        # 通知父组件处理空文件状态
        if self.parent():
            self.parent().on_files_dragged_out([])
    
    def get_file_count(self):
        """获取文件数量"""
        return len(self.file_paths)


# 导入QApplication用于拖拽距离
from PyQt6.QtWidgets import QApplication


class ImprovedShelfWidget(QWidget):
    """改进版本的Shelf窗口"""

    # 信号
    shelf_hidden = pyqtSignal()
    shelf_shown = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)

        # 配置
        self.FIXED_WIDTH = 200
        self.FIXED_HEIGHT = 200
        self.PADDING = 15
        self.BORDER_RADIUS = 12

        # 组件
        self.file_collection = None

        self.setup_window_properties()
        self.setup_layout()
        self.setup_styling()
        self.setup_drag_drop()

        print("ImprovedShelfWidget initialized")

    def setup_window_properties(self):
        """配置窗口属性"""
        # 设置窗口标志
        window_flags = (
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setWindowFlags(window_flags)

        # 设置半透明背景
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 设置固定大小
        self.resize(self.FIXED_WIDTH, self.FIXED_HEIGHT)

    def setup_layout(self):
        """设置布局"""
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(self.PADDING, self.PADDING,
                                          self.PADDING, self.PADDING)
        self.main_layout.setSpacing(10)

        # 占位符标签
        self.placeholder_label = QLabel("📁 拖拽文件到这里")
        self.placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.placeholder_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 180);
                font-size: 14px;
                font-weight: 500;
                padding: 20px;
                background: transparent;
            }
        """)
        self.main_layout.addWidget(self.placeholder_label)

    def setup_styling(self):
        """设置样式"""
        self.setStyleSheet("""
            ImprovedShelfWidget {
                background: rgba(30, 30, 30, 200);
                border: 1px solid rgba(255, 255, 255, 50);
                border-radius: 12px;
            }
        """)

    def setup_drag_drop(self):
        """设置拖放功能"""
        self.setAcceptDrops(True)
        print("Drag and drop enabled for improved shelf")

    def paintEvent(self, event):
        """自定义绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(40, 40, 40, 220))
        gradient.setColorAt(1, QColor(20, 20, 20, 240))

        # 绘制圆角矩形背景
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), self.BORDER_RADIUS, self.BORDER_RADIUS)

        super().paintEvent(event)

    def dragEnterEvent(self, event):
        """处理拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            print("📁 检测到文件拖拽")
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """处理拖拽移动事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """处理文件放置事件"""
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_paths.append(url.toLocalFile())

            if file_paths:
                print(f"📦 接收到文件: {[os.path.basename(p) for p in file_paths]}")
                self.add_files_to_collection(file_paths)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def add_files_to_collection(self, file_paths):
        """添加文件到集合"""
        # 如果还没有文件集合，创建一个
        if self.file_collection is None:
            self.create_file_collection()

        # 添加文件
        self.file_collection.add_files(file_paths)

        # 调整窗口大小
        self.adjust_size()

    def create_file_collection(self):
        """创建文件集合组件"""
        # 隐藏占位符
        self.placeholder_label.hide()

        # 创建文件集合
        self.file_collection = FileCollectionWidget(self)
        self.file_collection.files_dragged_out.connect(self.on_files_dragged_out)

        # 添加到布局
        self.main_layout.addWidget(self.file_collection)

        print("📦 创建文件集合组件")

    def on_files_dragged_out(self, file_paths):
        """处理文件被拖出事件"""
        print(f"🚀 文件被拖出: {len(file_paths)} 个")

        # 如果没有文件了，显示占位符
        if self.file_collection and self.file_collection.get_file_count() == 0:
            self.file_collection.hide()
            self.file_collection = None
            self.placeholder_label.show()
            self.adjust_size()

    def adjust_size(self):
        """调整窗口大小（仅在展开收缩时改变）"""
        if self.file_collection and self.file_collection.isVisible() and self.file_collection.is_expanded:
            # 展开状态：根据文件数量调整高度
            file_count = self.file_collection.get_file_count()
            if file_count > 0:
                # 计算展开后的高度
                expanded_height = self.FIXED_HEIGHT + min(file_count * 30, 150)  # 最多增加150px
                self.resize(self.FIXED_WIDTH, expanded_height)
                print(f"📏 展开状态 - 窗口大小调整为: {self.FIXED_WIDTH}x{expanded_height}")
            else:
                self.resize(self.FIXED_WIDTH, self.FIXED_HEIGHT)
                print(f"📏 展开状态（无文件）- 窗口大小调整为: {self.FIXED_WIDTH}x{self.FIXED_HEIGHT}")
        else:
            # 收起状态或无文件：固定大小
            self.resize(self.FIXED_WIDTH, self.FIXED_HEIGHT)
            print(f"📏 收起状态 - 窗口大小调整为: {self.FIXED_WIDTH}x{self.FIXED_HEIGHT}")

    def show_at_position(self, x, y):
        """在指定位置显示shelf"""
        try:
            # 清除启动消息（如果存在）
            self.clear_startup_message()

            # 调整窗口大小
            self.adjust_size()

            # 确保位置在屏幕范围内
            screen = QApplication.primaryScreen().geometry()
            x = max(0, min(x, screen.width() - self.width()))
            y = max(0, min(y, screen.height() - self.height()))

            self.move(x, y)
            self.show()
            self.raise_()
            self.activateWindow()

            self.shelf_shown.emit()
            print(f"✅ 改进版Shelf显示在 ({x}, {y})")

        except Exception as e:
            print(f"❌ 显示shelf时出错: {e}")

    def show_startup_message(self, x, y):
        """显示启动成功消息"""
        try:
            # 临时隐藏文件集合和占位符
            if self.file_collection:
                self.file_collection.hide()
            self.placeholder_label.hide()

            # 创建启动消息标签
            if not hasattr(self, 'startup_label'):
                self.startup_label = QLabel("🎉 启动成功！")
                self.startup_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.startup_label.setStyleSheet("""
                    QLabel {
                        color: rgba(255, 255, 255, 255);
                        font-size: 16px;
                        font-weight: bold;
                        padding: 20px;
                        background: transparent;
                    }
                """)
                self.main_layout.addWidget(self.startup_label)

            # 显示启动消息
            self.startup_label.show()

            # 设置窗口大小和位置
            self.resize(self.FIXED_WIDTH, self.FIXED_HEIGHT)

            # 确保位置在屏幕范围内
            screen = QApplication.primaryScreen().geometry()
            x = max(0, min(x, screen.width() - self.width()))
            y = max(0, min(y, screen.height() - self.height()))

            self.move(x, y)
            self.show()
            self.raise_()
            self.activateWindow()

            print(f"🎉 启动成功窗口显示在 ({x}, {y})")

        except Exception as e:
            print(f"❌ 显示启动消息时出错: {e}")

    def clear_startup_message(self):
        """清除启动消息"""
        try:
            # 隐藏并移除启动消息标签
            if hasattr(self, 'startup_label'):
                self.startup_label.hide()
                self.main_layout.removeWidget(self.startup_label)
                self.startup_label.deleteLater()
                delattr(self, 'startup_label')
                print("🧹 启动消息已清除")

            # 恢复正常显示
            if self.file_collection:
                self.file_collection.show()
            else:
                self.placeholder_label.show()

        except Exception as e:
            print(f"❌ 清除启动消息时出错: {e}")

    def hide_shelf(self):
        """隐藏shelf"""
        # 隐藏启动消息
        if hasattr(self, 'startup_label'):
            self.startup_label.hide()

        # 恢复正常显示
        if self.file_collection:
            self.file_collection.show()
        else:
            self.placeholder_label.show()

        self.hide()
        self.shelf_hidden.emit()
        print("🙈 Shelf已隐藏")

    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("🚪 按下Esc键 - 隐藏shelf")
            self.hide_shelf()
        super().keyPressEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件 - 处理右键关闭"""
        if event.button() == Qt.MouseButton.RightButton:
            print("🖱️ 右键点击 - 关闭shelf")
            self.hide_shelf()
        super().mousePressEvent(event)
